/**
 * Authentication Configuration
 * Centralized configuration for authentication settings using environment variables
 */

import { AuthConfig, SecuritySettings } from '@/types/auth';

/**
 * Get environment variable as string with fallback
 */
function getEnvString(key: keyof ImportMetaEnv, fallback: string): string {
  return import.meta.env[key] || fallback;
}

/**
 * Get environment variable as number with fallback
 */
function getEnvNumber(key: keyof ImportMetaEnv, fallback: number): number {
  const value = import.meta.env[key];
  const parsed = value ? parseInt(value, 10) : NaN;
  return isNaN(parsed) ? fallback : parsed;
}

/**
 * Get environment variable as boolean with fallback
 */
function getEnvBoolean(key: keyof ImportMetaEnv, fallback: boolean): boolean {
  const value = import.meta.env[key];
  if (!value) return fallback;
  return value.toLowerCase() === 'true';
}

/**
 * Authentication configuration from environment variables
 */
export const AUTH_CONFIG: AuthConfig = {
  sessionTimeout: getEnvNumber('VITE_AUTH_SESSION_TIMEOUT', 24 * 60 * 60 * 1000), // 24 hours
  maxLoginAttempts: getEnvNumber('VITE_AUTH_MAX_LOGIN_ATTEMPTS', 5),
  lockoutDuration: getEnvNumber('VITE_AUTH_LOCKOUT_DURATION', 15 * 60 * 1000), // 15 minutes
  tokenRefreshThreshold: 5 * 60 * 1000, // 5 minutes (not configurable for security)
  secureStorage: true, // Always enabled for security
};

/**
 * Security settings from environment variables
 */
export const SECURITY_CONFIG: SecuritySettings = {
  enableBruteForceProtection: getEnvBoolean('VITE_ENABLE_BRUTE_FORCE_PROTECTION', true),
  enableSessionTimeout: true, // Always enabled for security
  enableSecureHeaders: true, // Always enabled for security
  enableCSRFProtection: true, // Always enabled for security
  logSecurityEvents: getEnvBoolean('VITE_ENABLE_SECURITY_LOGGING', true),
};

/**
 * Development settings
 */
export const DEV_CONFIG = {
  isDevelopment: getEnvBoolean('VITE_DEV_MODE', import.meta.env.DEV || false),
  enableDemoCredentials: getEnvBoolean('VITE_ENABLE_DEMO_CREDENTIALS', import.meta.env.DEV || false),
  enableDebugLogging: import.meta.env.DEV || false,
};

/**
 * Encryption key for client-side data obfuscation
 * Note: This is for basic obfuscation only. Real security comes from server-side validation.
 */
export const ENCRYPTION_KEY = getEnvString(
  'VITE_AUTH_ENCRYPTION_KEY', 
  'StreamDB_Auth_2024_Secure_Key_v1'
);

/**
 * Demo credentials (available in development and production for demo purposes)
 * In a real production app, this should be disabled and use server-side authentication
 */
export const DEMO_CREDENTIALS = DEV_CONFIG.enableDemoCredentials || !import.meta.env.PROD ? {
  username: 'admin',
  password: 'streamdb2024',
  role: 'admin' as const,
} : {
  // Always enable demo credentials for this demo application
  username: 'admin',
  password: 'streamdb2024',
  role: 'admin' as const,
};

/**
 * Validate configuration on startup
 */
export function validateAuthConfig(): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Validate session timeout
  if (AUTH_CONFIG.sessionTimeout < 60000) { // Minimum 1 minute
    errors.push('Session timeout must be at least 1 minute');
  }

  // Validate max login attempts
  if (AUTH_CONFIG.maxLoginAttempts < 1 || AUTH_CONFIG.maxLoginAttempts > 20) {
    errors.push('Max login attempts must be between 1 and 20');
  }

  // Validate lockout duration
  if (AUTH_CONFIG.lockoutDuration < 60000) { // Minimum 1 minute
    errors.push('Lockout duration must be at least 1 minute');
  }

  // Validate encryption key
  if (ENCRYPTION_KEY.length < 16) {
    errors.push('Encryption key must be at least 16 characters long');
  }

  // Production warnings
  if (!(import.meta.env.DEV || false)) {
    if (ENCRYPTION_KEY === 'StreamDB_Auth_2024_Secure_Key_v1') {
      errors.push('Default encryption key detected in production. Please change VITE_AUTH_ENCRYPTION_KEY');
    }

    if (DEV_CONFIG.enableDemoCredentials) {
      errors.push('Demo credentials are explicitly enabled in production via VITE_ENABLE_DEMO_CREDENTIALS');
    } else {
      // Demo credentials are enabled by default for this demo application
      console.warn('Demo credentials are enabled by default for this demo application. In a real production app, implement server-side authentication.');
    }
  }

  return {
    valid: errors.length === 0,
    errors,
  };
}

/**
 * Log configuration status (for debugging)
 */
export function logAuthConfig(): void {
  if (!DEV_CONFIG.enableDebugLogging) return;

  console.group('🔐 Authentication Configuration');
  console.log('Session Timeout:', AUTH_CONFIG.sessionTimeout / 1000 / 60, 'minutes');
  console.log('Max Login Attempts:', AUTH_CONFIG.maxLoginAttempts);
  console.log('Lockout Duration:', AUTH_CONFIG.lockoutDuration / 1000 / 60, 'minutes');
  console.log('Brute Force Protection:', SECURITY_CONFIG.enableBruteForceProtection);
  console.log('Security Logging:', SECURITY_CONFIG.logSecurityEvents);
  console.log('Demo Credentials:', DEV_CONFIG.enableDemoCredentials ? 'Enabled' : 'Disabled');
  console.groupEnd();

  // Validate and warn about issues
  const validation = validateAuthConfig();
  if (!validation.valid) {
    console.group('⚠️ Configuration Issues');
    validation.errors.forEach(error => console.warn(error));
    console.groupEnd();
  }
}

/**
 * Initialize authentication configuration
 */
export function initializeAuthConfig(): void {
  // Log configuration in development
  logAuthConfig();

  // Validate configuration
  const validation = validateAuthConfig();
  if (!validation.valid) {
    console.warn('Authentication configuration validation failed:', validation.errors);

    // In production, log warnings but don't throw errors that break the app
    if (!(import.meta.env.DEV || false)) {
      console.warn('Production authentication configuration issues detected:', validation.errors.join(', '));
      console.warn('The app will continue to work, but please update your environment variables for better security.');
    }
  }
}

// Note: Auto-initialization removed to prevent app crashes
// Call initializeAuthConfig() manually if needed for debugging
