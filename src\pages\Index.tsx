
import HeroCarousel from "@/components/HeroCarousel";
import CardGrid from "@/components/CardGrid";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { mediaData } from "@/data/movies";
import { Link } from "react-router-dom";
import PromoBannerContainer from "@/components/PromoBannerContainer";
import { scrollToTop } from "@/utils/scrollToTop";
import { getHomepageContent } from "@/utils/contentFilters";

const Index = () => {
  // Get properly filtered content for homepage sections
  const { movies, series, requested, featured, carousel } = getHomepageContent(mediaData, 20);

  return (
    <div className="flex flex-col min-h-screen bg-background">
      <Header />

      {/* Added spacing here after Header */}
      <div className="h-7 md:h-10" />

      <main className="flex-1 w-full max-w-7xl mx-auto px-3 sm:px-4 py-2 sm:py-4">
        {/* Increased margin-bottom for HeroCarousel */}
        <div className="mb-6 sm:mb-9 md:mb-14">
          <HeroCarousel />
        </div>

        {/* Proper gap between sections */}
        <div className="mb-8 sm:mb-12">
          <PromoBannerContainer />
        </div>

        {/* Movies Section */}
        <section className="stdb-section mb-10 sm:mb-14">
          <div className="flex items-end justify-between mb-3 sm:mb-4">
            <h3
              className="
                text-2xl sm:text-3xl md:text-4xl
                font-black
                font-mono
                text-primary
                drop-shadow-[0_2px_10px_rgba(34,197,94,0.4)]
                tracking-wide
                uppercase
                leading-tight
                select-none
                "
              style={{
                letterSpacing: "1px",
              }}
            >
              Movies
            </h3>
            <Link
              to="/movies"
              className="text-primary font-bold underline underline-offset-4 text-sm sm:text-base hover:opacity-80 transition flex-shrink-0"
              onClick={scrollToTop}
            >
              Show all
            </Link>
          </div>
          <CardGrid items={movies} />
        </section>

        {/* Web Series Section */}
        <section className="stdb-section mb-10 sm:mb-14">
          <div className="flex items-end justify-between mb-3 sm:mb-4">
            <h3
              className="
                text-2xl sm:text-3xl md:text-4xl
                font-black
                font-mono
                text-primary
                drop-shadow-[0_2px_10px_rgba(34,197,94,0.4)]
                tracking-wide
                uppercase
                leading-tight
                select-none
                "
              style={{
                letterSpacing: "1px",
              }}
            >
              Web Series
            </h3>
            <Link
              to="/series"
              className="text-primary font-bold underline underline-offset-4 text-sm sm:text-base hover:opacity-80 transition flex-shrink-0"
              onClick={scrollToTop}
            >
              Show all
            </Link>
          </div>
          <CardGrid items={series} />
        </section>

        {/* Requested Section */}
        <section className="stdb-section mb-12 sm:mb-16">
          <div className="flex items-end justify-between mb-3 sm:mb-4">
            <h3
              className="
                text-2xl sm:text-3xl md:text-4xl
                font-black
                font-mono
                text-primary
                drop-shadow-[0_2px_10px_rgba(34,197,94,0.4)]
                tracking-wide
                uppercase
                leading-tight
                select-none
                "
              style={{
                letterSpacing: "1px",
              }}
            >
              Requested
            </h3>
            <Link
              to="/requested"
              className="text-primary font-bold underline underline-offset-4 text-sm sm:text-base hover:opacity-80 transition flex-shrink-0"
              onClick={scrollToTop}
            >
              Show all
            </Link>
          </div>
          <CardGrid items={requested} />
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default Index;
