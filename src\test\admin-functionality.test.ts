// Test file to validate admin functionality
// This is a conceptual test - in a real environment you'd use Jest/Vitest

import { MediaItem, Episode, Season } from '../types/media';
import { ContentFormData, EpisodeFormData, SeasonFormData } from '../types/admin';

// Test data
const mockMovie: MediaItem = {
  id: "test-movie-1",
  title: "Test Movie",
  description: "A test movie for validation",
  year: 2024,
  genres: ["Action", "Sci-Fi"],
  type: "movie",
  image: "https://example.com/poster.jpg",
  coverImage: "https://example.com/cover.jpg",
  createdAt: "2024-01-01T00:00:00Z",
  isPublished: true,
  isFeatured: false,
  addToCarousel: false,
  updatedAt: "2024-01-01T00:00:00Z",
};

const mockSeries: MediaItem = {
  id: "test-series-1",
  title: "Test Series",
  description: "A test series for validation",
  year: 2024,
  genres: ["Drama", "Thriller"],
  type: "series",
  image: "https://example.com/series-poster.jpg",
  coverImage: "https://example.com/series-cover.jpg",
  createdAt: "2024-01-01T00:00:00Z",
  isPublished: true,
  isFeatured: true,
  addToCarousel: false,
  seasons: [],
  totalSeasons: 0,
  totalEpisodes: 0,
  updatedAt: "2024-01-01T00:00:00Z",
};

// Test functions
export function testContentFormValidation(formData: ContentFormData): boolean {
  // Basic validation tests
  if (!formData.title || formData.title.trim() === '') {
    console.error('Title is required');
    return false;
  }
  
  if (!formData.type || !['movie', 'series'].includes(formData.type)) {
    console.error('Valid type is required');
    return false;
  }
  
  if (!formData.year || isNaN(parseInt(formData.year))) {
    console.error('Valid year is required');
    return false;
  }
  
  console.log('Content form validation passed');
  return true;
}

export function testEpisodeManagement(): boolean {
  // Test episode creation
  const episode: Episode = {
    id: "test-episode-1",
    title: "Test Episode",
    season: 1,
    episode: 1,
    description: "A test episode",
    videoLink: "https://example.com/video.mp4",
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
  };
  
  // Test season creation
  const season: Season = {
    id: "test-season-1",
    seasonNumber: 1,
    title: "Season 1",
    episodes: [episode],
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
  };
  
  // Test adding episode to series
  const updatedSeries: MediaItem = {
    ...mockSeries,
    seasons: [season],
    totalSeasons: 1,
    totalEpisodes: 1,
    updatedAt: new Date().toISOString(),
  };
  
  if (updatedSeries.seasons?.length !== 1) {
    console.error('Season not added correctly');
    return false;
  }
  
  if (updatedSeries.seasons[0].episodes.length !== 1) {
    console.error('Episode not added correctly');
    return false;
  }
  
  console.log('Episode management test passed');
  return true;
}

export function testContentFiltering(): boolean {
  const testContent: MediaItem[] = [mockMovie, mockSeries];
  
  // Test type filtering
  const movies = testContent.filter(item => item.type === 'movie');
  const series = testContent.filter(item => item.type === 'series');
  
  if (movies.length !== 1 || series.length !== 1) {
    console.error('Type filtering failed');
    return false;
  }
  
  // Test featured filtering
  const featured = testContent.filter(item => item.isFeatured === true);
  
  if (featured.length !== 1 || featured[0].id !== mockSeries.id) {
    console.error('Featured filtering failed');
    return false;
  }
  
  // Test search functionality
  const searchResults = testContent.filter(item => 
    item.title.toLowerCase().includes('test') ||
    item.description.toLowerCase().includes('test')
  );
  
  if (searchResults.length !== 2) {
    console.error('Search filtering failed');
    return false;
  }
  
  console.log('Content filtering test passed');
  return true;
}

export function testDataIntegrity(): boolean {
  // Test that required fields are present
  const requiredFields = ['id', 'title', 'type', 'year', 'genres', 'createdAt'];
  
  for (const field of requiredFields) {
    if (!(field in mockMovie) || !(field in mockSeries)) {
      console.error(`Required field ${field} missing`);
      return false;
    }
  }
  
  // Test that series-specific fields are present for series
  const seriesFields = ['seasons', 'totalSeasons', 'totalEpisodes'];
  
  for (const field of seriesFields) {
    if (!(field in mockSeries)) {
      console.error(`Series field ${field} missing`);
      return false;
    }
  }
  
  console.log('Data integrity test passed');
  return true;
}

export function runAllTests(): boolean {
  console.log('Running admin functionality tests...');
  
  const testFormData: ContentFormData = {
    title: "Test Content",
    type: "movie",
    tmdbId: "123456",
    year: "2024",
    genres: ["Action"],
    languages: ["English"],
    description: "Test description",
    posterUrl: "https://example.com/poster.jpg",
    thumbnailUrl: "https://example.com/thumb.jpg",
    videoLinks: "https://example.com/video.mp4",
    quality: ["HD"],
    tags: "test, movie",
    imdbRating: "8.5",
    runtime: "120",
    studio: "Test Studio",
    audioTracks: ["English"],
    trailer: "https://example.com/trailer.mp4",
    subtitleFile: null,
    subtitleUrl: "https://example.com/subtitles.srt",
    isPublished: true,
    isFeatured: false,
    addToCarousel: false,
  };
  
  const tests = [
    () => testContentFormValidation(testFormData),
    testEpisodeManagement,
    testContentFiltering,
    testDataIntegrity,
  ];
  
  let allPassed = true;
  
  for (let i = 0; i < tests.length; i++) {
    try {
      const result = tests[i]();
      if (!result) {
        allPassed = false;
        console.error(`Test ${i + 1} failed`);
      }
    } catch (error) {
      allPassed = false;
      console.error(`Test ${i + 1} threw error:`, error);
    }
  }
  
  if (allPassed) {
    console.log('✅ All tests passed!');
  } else {
    console.log('❌ Some tests failed');
  }
  
  return allPassed;
}

// Export for use in browser console or testing framework
if (typeof window !== 'undefined') {
  (window as any).adminTests = {
    runAllTests,
    testContentFormValidation,
    testEpisodeManagement,
    testContentFiltering,
    testDataIntegrity,
  };
}
