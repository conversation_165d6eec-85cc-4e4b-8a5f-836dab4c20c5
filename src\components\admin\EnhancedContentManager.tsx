import { useState, useEffect, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { MediaItem } from "@/types/media";
import { ContentFilters, SortField, SortOrder } from "@/types/admin";
import { mediaData } from "@/data/movies";
import ContentEditDialog from "./ContentEditDialog";
import EpisodeManager from "./EpisodeManager";
import {
  Eye, Edit, Trash2, Search, Filter, ArrowUpDown,
  Star, Image, Play, Plus, Settings, Download,
  ChevronLeft, ChevronRight
} from "lucide-react";
import { scrollToTop } from "@/utils/scrollToTop";
import { getDefaultCategory } from "@/utils/contentFilters";

export default function EnhancedContentManager() {
  const { toast } = useToast();
  
  // State management
  const [content, setContent] = useState<MediaItem[]>([]);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState<SortField>('createdAt');
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc');
  const [filters, setFilters] = useState<ContentFilters>({});
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  
  // Dialog states
  const [selectedContent, setSelectedContent] = useState<MediaItem | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isEpisodeManagerOpen, setIsEpisodeManagerOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);

  // Initialize content from mediaData
  useEffect(() => {
    // Convert mediaData to enhanced format with additional fields
    const enhancedContent: MediaItem[] = mediaData.map(item => ({
      ...item,
      isPublished: true,
      isFeatured: Math.random() > 0.7, // Random featured status for demo
      addToCarousel: Math.random() > 0.8, // Random carousel status for demo
      updatedAt: item.createdAt,
      // Assign default category if not present (backward compatibility)
      category: item.category || getDefaultCategory(item),
      seasons: item.type === 'series' ? [] : undefined, // Initialize empty seasons for series
      totalSeasons: item.type === 'series' ? 0 : undefined,
      totalEpisodes: item.type === 'series' ? 0 : undefined,
    }));
    setContent(enhancedContent);
  }, []);

  // Filtered and sorted content
  const filteredContent = useMemo(() => {
    let filtered = [...content];

    // Apply filters
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(item => 
        item.title.toLowerCase().includes(searchLower) ||
        item.description.toLowerCase().includes(searchLower) ||
        item.genres.some(genre => genre.toLowerCase().includes(searchLower))
      );
    }

    if (filters.type) {
      filtered = filtered.filter(item => item.type === filters.type);
    }

    if (filters.featured !== undefined) {
      filtered = filtered.filter(item => item.isFeatured === filters.featured);
    }

    if (filters.carousel !== undefined) {
      filtered = filtered.filter(item => item.addToCarousel === filters.carousel);
    }

    if (filters.year) {
      filtered = filtered.filter(item => item.year === filters.year);
    }

    if (filters.genre) {
      filtered = filtered.filter(item => 
        item.genres.some(genre => genre.toLowerCase().includes(filters.genre!.toLowerCase()))
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any = a[sortBy];
      let bValue: any = b[sortBy];

      // Handle special cases
      if (sortBy === 'createdAt' || sortBy === 'updatedAt') {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      }

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    return filtered;
  }, [content, filters, sortBy, sortOrder]);

  // Pagination
  const totalPages = Math.ceil(filteredContent.length / itemsPerPage);
  const paginatedContent = filteredContent.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // Event handlers
  const handleSort = (field: SortField) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  const handleEdit = (item: MediaItem) => {
    setSelectedContent(item);
    setIsEditDialogOpen(true);
  };

  const handleView = (item: MediaItem) => {
    setSelectedContent(item);
    setIsViewDialogOpen(true);
  };

  const handleManageEpisodes = (item: MediaItem) => {
    if (item.type === 'series') {
      setSelectedContent(item);
      setIsEpisodeManagerOpen(true);
    }
  };

  const handleDelete = (id: string) => {
    const item = content.find(c => c.id === id);
    if (window.confirm(`Are you sure you want to delete "${item?.title}"? This action cannot be undone.`)) {
      setContent(prev => prev.filter(c => c.id !== id));
      toast({
        title: "Content deleted",
        description: `"${item?.title}" has been deleted successfully`,
      });
    }
  };

  const handleSaveContent = (updatedContent: MediaItem) => {
    setContent(prev => prev.map(item => 
      item.id === updatedContent.id ? updatedContent : item
    ));
  };

  const handleToggleFeature = (id: string, field: 'isFeatured' | 'addToCarousel') => {
    setContent(prev => prev.map(item => 
      item.id === id ? { ...item, [field]: !item[field], updatedAt: new Date().toISOString() } : item
    ));
    const item = content.find(c => c.id === id);
    toast({
      title: "Feature updated",
      description: `${field === 'isFeatured' ? 'Featured' : 'Carousel'} ${item?.[field] ? 'disabled' : 'enabled'} for "${item?.title}"`,
    });
  };

  const toggleSelectAll = () => {
    if (selectedItems.length === paginatedContent.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(paginatedContent.map(item => item.id));
    }
  };

  const handleBulkDelete = () => {
    if (selectedItems.length === 0) return;

    if (window.confirm(`Are you sure you want to delete ${selectedItems.length} selected items?`)) {
      setContent(prev => prev.filter(item => !selectedItems.includes(item.id)));
      setSelectedItems([]);
      toast({
        title: "Bulk delete completed",
        description: `${selectedItems.length} items have been deleted`,
      });
    }
  };

  // Function to handle page changes with scroll to top
  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
    scrollToTop();
  };

  // Helper function to escape CSV values and handle special characters
  const escapeCsvValue = (value: any): string => {
    if (value === null || value === undefined) return '';
    const stringValue = String(value);
    // Escape quotes by doubling them and wrap in quotes if contains comma, quote, or newline
    if (stringValue.includes('"') || stringValue.includes(',') || stringValue.includes('\n') || stringValue.includes('\r')) {
      return `"${stringValue.replace(/"/g, '""')}"`;
    }
    return stringValue;
  };

  const exportData = (format: 'csv' | 'json') => {
    const dataToExport = selectedItems.length > 0
      ? content.filter(item => selectedItems.includes(item.id))
      : filteredContent;

    if (format === 'csv') {
      // Comprehensive CSV headers including all content fields
      const csvHeaders = [
        'Title', 'Description', 'Type', 'Category', 'Year', 'Genres', 'Languages', 'Status',
        'Featured', 'Carousel', 'IMDb Rating', 'Runtime', 'Studio', 'Tags',
        'Poster URL', 'Thumbnail URL', 'Cover Image', 'Trailer URL', 'Subtitle URL',
        'Video Links', 'Secure Video Links', 'Quality', 'Audio Tracks',
        'TMDB ID', 'Total Seasons', 'Total Episodes', 'Created At', 'Updated At'
      ];

      const csvData = dataToExport.map(item => {
        // Handle seasons/episodes data for web series
        const seasonsInfo = item.type === 'series' && item.seasons ?
          item.seasons.map(season =>
            `Season ${season.seasonNumber}: ${season.episodes?.length || 0} episodes`
          ).join('; ') : '';

        // Combine all video links (both legacy and secure)
        const allVideoLinks = [
          item.videoLinks || '',
          item.secureVideoLinks || ''
        ].filter(link => link.trim()).join(' | ');

        return [
          escapeCsvValue(item.title),
          escapeCsvValue(item.description),
          escapeCsvValue(item.type === 'movie' ? 'Movie' : 'Web Series'),
          escapeCsvValue(item.category || ''),
          escapeCsvValue(item.year),
          escapeCsvValue(item.genres?.join('; ') || ''),
          escapeCsvValue(item.languages?.join('; ') || ''),
          escapeCsvValue(item.isPublished ? 'Published' : 'Draft'),
          escapeCsvValue(item.isFeatured ? 'Yes' : 'No'),
          escapeCsvValue(item.addToCarousel ? 'Yes' : 'No'),
          escapeCsvValue(item.imdbRating || ''),
          escapeCsvValue(item.runtime || ''),
          escapeCsvValue(item.studio || ''),
          escapeCsvValue(item.tags || ''),
          escapeCsvValue(item.posterUrl || item.image || ''),
          escapeCsvValue(item.thumbnailUrl || ''),
          escapeCsvValue(item.coverImage || ''),
          escapeCsvValue(item.trailer || ''),
          escapeCsvValue(item.subtitleUrl || ''),
          escapeCsvValue(item.videoLinks || ''),
          escapeCsvValue(item.secureVideoLinks || ''),
          escapeCsvValue(item.quality?.join('; ') || ''),
          escapeCsvValue(item.audioTracks?.join('; ') || ''),
          escapeCsvValue(item.tmdbId || ''),
          escapeCsvValue(item.totalSeasons || ''),
          escapeCsvValue(item.totalEpisodes || ''),
          escapeCsvValue(item.createdAt || ''),
          escapeCsvValue(item.updatedAt || '')
        ].join(',');
      });

      const csvContent = [csvHeaders.join(','), ...csvData].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'content-export.csv';
      a.click();
      URL.revokeObjectURL(url);
    } else {
      const jsonContent = JSON.stringify(dataToExport, null, 2);
      const blob = new Blob([jsonContent], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'content-export.json';
      a.click();
      URL.revokeObjectURL(url);
    }

    toast({
      title: "Export completed",
      description: `Data exported as ${format.toUpperCase()} successfully`,
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-primary">Manage Content</h2>
          <p className="text-muted-foreground">
            {filteredContent.length} of {content.length} items
            {selectedItems.length > 0 && ` • ${selectedItems.length} selected`}
          </p>
        </div>
        
        <div className="flex gap-2">
          {selectedItems.length > 0 && (
            <>
              <Button variant="outline" size="sm" onClick={handleBulkDelete}>
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Selected
              </Button>
              <Button variant="outline" size="sm" onClick={() => exportData('csv')}>
                <Download className="h-4 w-4 mr-2" />
                Export CSV
              </Button>
            </>
          )}
          <Button variant="outline" size="sm" onClick={() => exportData('csv')}>
            <Download className="h-4 w-4 mr-2" />
            Export CSV
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search content..."
            value={filters.search || ''}
            onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
            className="pl-10"
          />
        </div>
        
        <select
          value={filters.type || ''}
          onChange={(e) => setFilters(prev => ({ ...prev, type: e.target.value || undefined }))}
          className="h-10"
        >
          <option value="">All Types</option>
          <option value="movie">Movies</option>
          <option value="series">Web Series</option>
        </select>

        <select
          value={filters.featured === undefined ? '' : filters.featured.toString()}
          onChange={(e) => setFilters(prev => ({
            ...prev,
            featured: e.target.value === '' ? undefined : e.target.value === 'true'
          }))}
          className="h-10"
        >
          <option value="">All Featured</option>
          <option value="true">Featured</option>
          <option value="false">Not Featured</option>
        </select>

        <Button
          variant="outline"
          onClick={() => setFilters({})}
          disabled={Object.keys(filters).length === 0}
        >
          <Filter className="h-4 w-4 mr-2" />
          Clear
        </Button>
      </div>

      {/* Content Table */}
      <div className="border border-border rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <input
                  type="checkbox"
                  checked={selectedItems.length === paginatedContent.length && paginatedContent.length > 0}
                  onChange={toggleSelectAll}
                  className="rounded"
                />
              </TableHead>
              <TableHead 
                className="cursor-pointer hover:bg-muted/50"
                onClick={() => handleSort('title')}
              >
                <div className="flex items-center gap-2">
                  Title
                  <ArrowUpDown className="h-4 w-4" />
                </div>
              </TableHead>
              <TableHead 
                className="cursor-pointer hover:bg-muted/50"
                onClick={() => handleSort('type')}
              >
                <div className="flex items-center gap-2">
                  Type
                  <ArrowUpDown className="h-4 w-4" />
                </div>
              </TableHead>
              <TableHead 
                className="cursor-pointer hover:bg-muted/50"
                onClick={() => handleSort('year')}
              >
                <div className="flex items-center gap-2">
                  Year
                  <ArrowUpDown className="h-4 w-4" />
                </div>
              </TableHead>
              <TableHead>Genres</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Features</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedContent.map((item) => (
              <TableRow key={item.id}>
                <TableCell>
                  <input
                    type="checkbox"
                    checked={selectedItems.includes(item.id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedItems(prev => [...prev, item.id]);
                      } else {
                        setSelectedItems(prev => prev.filter(id => id !== item.id));
                      }
                    }}
                    className="rounded"
                  />
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-3">
                    <img
                      src={item.image}
                      alt={item.title}
                      className="w-10 h-15 object-cover rounded"
                    />
                    <div>
                      <div className="font-medium">{item.title}</div>
                      <div className="text-sm text-muted-foreground">
                        {item.studio || 'Unknown Studio'}
                      </div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant={item.type === 'movie' ? 'default' : 'secondary'}>
                    {item.type === 'movie' ? 'Movie' : 'Web Series'}
                  </Badge>
                </TableCell>
                <TableCell>{item.year}</TableCell>
                <TableCell>
                  <div className="flex flex-wrap gap-1">
                    {item.genres.slice(0, 2).map(genre => (
                      <Badge key={genre} variant="outline" className="text-xs">
                        {genre}
                      </Badge>
                    ))}
                    {item.genres.length > 2 && (
                      <Badge variant="outline" className="text-xs">
                        +{item.genres.length - 2}
                      </Badge>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant={item.isPublished ? 'default' : 'secondary'}>
                    {item.isPublished ? 'Published' : 'Draft'}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="flex gap-1">
                    <button
                      onClick={() => handleToggleFeature(item.id, 'isFeatured')}
                      className={`p-1 rounded ${item.isFeatured ? 'text-yellow-500' : 'text-muted-foreground'}`}
                      title="Toggle Featured"
                    >
                      <Star className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleToggleFeature(item.id, 'addToCarousel')}
                      className={`p-1 rounded ${item.addToCarousel ? 'text-blue-500' : 'text-muted-foreground'}`}
                      title="Toggle Carousel"
                    >
                      <Image className="h-4 w-4" />
                    </button>
                  </div>
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex gap-2 justify-end">
                    <Button variant="ghost" size="sm" onClick={() => handleView(item)}>
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => handleEdit(item)}>
                      <Edit className="h-4 w-4" />
                    </Button>
                    {item.type === 'series' && (
                      <Button variant="ghost" size="sm" onClick={() => handleManageEpisodes(item)}>
                        <Play className="h-4 w-4" />
                      </Button>
                    )}
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="text-destructive hover:text-destructive"
                      onClick={() => handleDelete(item.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing {(currentPage - 1) * itemsPerPage + 1} to {Math.min(currentPage * itemsPerPage, filteredContent.length)} of {filteredContent.length} results
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>

            <div className="flex items-center gap-1">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const page = i + 1;
                return (
                  <Button
                    key={page}
                    variant={currentPage === page ? "default" : "outline"}
                    size="sm"
                    onClick={() => handlePageChange(page)}
                  >
                    {page}
                  </Button>
                );
              })}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Dialogs */}
      <ContentEditDialog
        isOpen={isEditDialogOpen}
        onClose={() => setIsEditDialogOpen(false)}
        content={selectedContent}
        onSave={handleSaveContent}
      />

      <EpisodeManager
        isOpen={isEpisodeManagerOpen}
        onClose={() => setIsEpisodeManagerOpen(false)}
        content={selectedContent}
        onSave={handleSaveContent}
      />
    </div>
  );
}
